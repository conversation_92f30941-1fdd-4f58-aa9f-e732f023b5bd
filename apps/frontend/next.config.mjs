/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // Keep disabled for now to avoid blocking builds
  },
  typescript: {
    ignoreBuildErrors: true, // Keep disabled for now to avoid blocking builds
  },
  images: {
    unoptimized: true,
  },
  // API rewrites for development
  async rewrites() {
    // For Docker builds, always use backend service name
    // For local development, use localhost
    const backendUrl = process.env.NODE_ENV === 'production' || process.env.INTERNAL_API_URL
      ? 'http://backend:8000'
      : 'http://localhost:8000';

    console.log('Next.js rewrites - Backend URL:', backendUrl);

    return [
      {
        source: '/api/:path*',
        destination: `${backendUrl}/api/:path*`,
      },
    ];
  },
  // Production optimizations
  // experimental: {
  //   optimizeCss: true, // Disabled due to critters dependency issue
  // },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Performance optimizations
  poweredByHeader: false,
  compress: true,
}

export default nextConfig
